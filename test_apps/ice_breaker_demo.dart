import 'package:flutter/material.dart';
import '../lib/models/user.dart';
import '../lib/components/ice_breaker_widget.dart';
import '../lib/theme/app_design_system.dart';

void main() {
  runApp(const IceBreakerDemoApp());
}

class IceBreakerDemoApp extends StatelessWidget {
  const IceBreakerDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ice Breaker Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const IceBreakerDemoPage(),
    );
  }
}

class IceBreakerDemoPage extends StatefulWidget {
  const IceBreakerDemoPage({super.key});

  @override
  State<IceBreakerDemoPage> createState() => _IceBreakerDemoPageState();
}

class _IceBreakerDemoPageState extends State<IceBreakerDemoPage> {
  final TextEditingController _messageController = TextEditingController();
  String _selectedTopic = '';

  final User _testUser = User(
    id: 'test-lawyer',
    name: 'AI Lawyer',
    role: 'lawyer',
    avatar: 'lawyer.png',
    description: 'Professional legal assistant',
    tags: ['legal', 'professional', 'advice'],
    personality: 'Professional and knowledgeable',
    isOnline: true,
  );

  void _onTopicSelected(String topic) {
    setState(() {
      _selectedTopic = topic;
      _messageController.text = topic;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected: $topic'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      appBar: AppBar(
        title: const Text('Ice Breaker Demo'),
        backgroundColor: AppDesignSystem.primaryYellow,
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'AI Character: ${_testUser.name}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Role: ${_testUser.role}',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Personality: ${_testUser.personality}',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'Ice Breaker Topics:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (_selectedTopic.isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Selected Topic:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _selectedTopic,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
          ),
          // Ice Breaker Widget
          IceBreakerWidget(
            aiUser: _testUser,
            onTopicSelected: _onTopicSelected,
            isVisible: true,
          ),
          // Message Input Area
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: TextField(
                      controller: _messageController,
                      decoration: const InputDecoration(
                        hintText: 'Type a message...',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: AppDesignSystem.primaryYellow,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.send, color: Colors.white),
                    onPressed: () {
                      final message = _messageController.text.trim();
                      if (message.isNotEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Sent: $message'),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                        _messageController.clear();
                        setState(() {
                          _selectedTopic = '';
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

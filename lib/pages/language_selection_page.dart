import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/language_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../theme/app_design_system.dart';

class LanguageSelectionPage extends StatelessWidget {
  const LanguageSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppDesignSystem.backgroundPrimary,
      appBar: AppBar(
        backgroundColor: AppDesignSystem.backgroundPrimary,
        title: Text(
          l10n.language,
          style: const TextStyle(color: AppDesignSystem.textPrimary),
        ),
        leading: I<PERSON><PERSON><PERSON>on(
          icon: const Icon(Icons.arrow_back, color: AppDesignSystem.primaryBlack),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
      ),
      body: Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildLanguageOption(
                context,
                languageProvider,
                'en',
                l10n.english,
                'English',
              ),
              _buildLanguageOption(
                context,
                languageProvider,
                'zh',
                l10n.traditionalChinese,
                '中文',
              ),
              _buildLanguageOption(
                context,
                languageProvider,
                'ar',
                l10n.arabic,
                'العربية',
              ),
              _buildLanguageOption(
                context,
                languageProvider,
                'hi',
                l10n.hindi,
                'हिंदी',
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    LanguageProvider languageProvider,
    String languageCode,
    String translatedName,
    String nativeName,
  ) {
    final isSelected = languageProvider.locale.languageCode == languageCode;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: isSelected ? AppDesignSystem.primaryYellow.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: isSelected ? AppDesignSystem.primaryBlack: Colors.grey, width: isSelected ? 1.5: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () async {
            await languageProvider.changeLanguage(languageCode);
            // 延迟一点让用户看到选中效果
            await Future.delayed(const Duration(milliseconds: 200));
            if (context.mounted) {
              Navigator.pop(context);
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        translatedName,
                        style: TextStyle(
                          color: AppDesignSystem.textPrimary,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        nativeName,
                        style: TextStyle(
                          color: AppDesignSystem.primaryBlack.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? AppDesignSystem.primaryYellow : Colors.transparent,
                    border: Border.all(
                      color: isSelected ? AppDesignSystem.primaryYellow : Colors.grey,
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
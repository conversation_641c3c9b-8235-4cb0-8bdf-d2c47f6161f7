import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import '../models/topic.dart';
import '../models/user.dart';
import 'ai_data_service.dart';

/// 今日话题服务类
class DailyTopicService {
  static final DailyTopicService _instance = DailyTopicService._internal();
  factory DailyTopicService() => _instance;
  DailyTopicService._internal();

  Map<String, dynamic>? _topicData;
  List<User>? _allUsers;

  /// 加载话题数据
  Future<void> loadTopicData() async {
    if (_topicData != null) return;

    try {
      final String jsonString = await rootBundle.loadString('assets/data/daily_topics.json');
      _topicData = json.decode(jsonString);
    } catch (e) {
      debugPrint('Error loading daily topic data: $e');
      _topicData = {};
    }
  }

  /// 加载用户数据
  Future<void> _loadUsersIfNeeded() async {
    if (_allUsers != null) return;
    
    try {
      _allUsers = await AIDataService().getAllUsers();
    } catch (e) {
      debugPrint('Error loading users for topics: $e');
      _allUsers = [];
    }
  }

  /// 获取当前语言代码
  String _getLanguageCode(Locale locale) {
    switch (locale.languageCode) {
      case 'zh':
        return 'zh';
      case 'ar':
        return 'ar';
      case 'hi':
        return 'hi';
      default:
        return 'en';
    }
  }

  /// 获取所有话题
  Future<List<DailyTopic>> getAllTopics() async {
    await loadTopicData();
    
    final topicsJson = _topicData?['topics'] as List<dynamic>? ?? [];
    return topicsJson.map((json) => DailyTopic.fromJson(json)).toList();
  }

  /// 获取今日推荐话题（最多显示6个）
  Future<List<DailyTopic>> getTodayTopics({int maxCount = 6}) async {
    final allTopics = await getAllTopics();
    
    // 按优先级排序，然后取前maxCount个
    allTopics.sort((a, b) => a.priority.compareTo(b.priority));
    return allTopics.take(maxCount).toList();
  }

  /// 根据话题ID查找对应的AI用户
  Future<User?> getAIUserForTopic(DailyTopic topic) async {
    if (topic.aiUserId == null) return null;
    
    await _loadUsersIfNeeded();
    
    try {
      return _allUsers?.firstWhere((user) => user.id == topic.aiUserId);
    } catch (e) {
      debugPrint('AI user not found for topic ${topic.id}: $e');
      return null;
    }
  }

  /// 获取话题的本地化文本
  String getTopicText(DailyTopic topic, Locale locale, Map<String, String> localizations) {
    // 直接使用textKey从localizations中获取文本
    String? text = localizations[topic.textKey];

    // 如果没有找到，返回textKey作为fallback
    return text ?? topic.textKey;
  }

  /// 根据分类获取话题
  Future<List<DailyTopic>> getTopicsByCategory(String category) async {
    final allTopics = await getAllTopics();
    return allTopics.where((topic) => topic.category == category).toList();
  }

  /// 获取随机话题
  Future<List<DailyTopic>> getRandomTopics({int count = 3}) async {
    final allTopics = await getAllTopics();
    allTopics.shuffle();
    return allTopics.take(count).toList();
  }
}

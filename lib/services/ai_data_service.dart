import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/user.dart';

class AIDataService {
  static final AIDataService _instance = AIDataService._internal();
  static List<User>? _allUsers;
  static final Random _random = Random();

  factory AIDataService() {
    return _instance;
  }

  AIDataService._internal();

  /// Load all AI users from JSON file
  Future<List<User>> loadAllUsers() async {
    if (_allUsers != null) {
      return _allUsers!;
    }

    try {
      final String jsonString = await rootBundle.loadString('assets/data/ai_users.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> userList = jsonData['ai_users'];

      _allUsers = userList.map((userData) {
        return User(
          id: userData['id'],
          name: userData['name'],
          avatar: userData['avatar'],
          description: userData['description'],
          tags: List<String>.from(userData['tags']),
          personality: userData['personality'] ?? '',
          role: userData['role'] ?? 'assistant',
          isOnline: true, // All AI users are always online
        );
      }).toList();

      return _allUsers!;
    } catch (e) {
      debugPrint('Error loading AI users: $e');
      return [];
    }
  }

  /// Get random AI users for recommendations
  Future<List<User>> getRandomUsers({int count = 5}) async {
    final allUsers = await loadAllUsers();
    if (allUsers.isEmpty) return [];

    if (count >= allUsers.length) {
      return List.from(allUsers)..shuffle();
    }

    final shuffled = List<User>.from(allUsers);
    shuffled.shuffle(_random);
    return shuffled.take(count).toList();
  }

  /// Get a specific user by ID
  Future<User?> getUserById(String id) async {
    final allUsers = await loadAllUsers();
    try {
      return allUsers.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get all users
  Future<List<User>> getAllUsers() async {
    return await loadAllUsers();
  }

  /// Get users by personality type
  Future<List<User>> getUsersByPersonality(String personality) async {
    final allUsers = await loadAllUsers();
    if (personality.isEmpty) return [];
    
    final lowerPersonality = personality.toLowerCase();
    
    return allUsers.where((user) {
      return user.personality.toLowerCase().contains(lowerPersonality);
    }).toList();
  }

  /// Get users by role
  Future<List<User>> getUsersByRole(String role) async {
    final allUsers = await loadAllUsers();
    if (role.isEmpty) return [];
    
    return allUsers.where((user) {
      return user.role == role;
    }).toList();
  }

  /// Search users by name or profession
  Future<List<User>> searchUsers(String query) async {
    if (query.isEmpty) return [];
    
    final allUsers = await loadAllUsers();
    final lowerQuery = query.toLowerCase();
    
    return allUsers.where((user) {
      return user.name.toLowerCase().contains(lowerQuery) ||
             user.description.toLowerCase().contains(lowerQuery) ||
             user.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// Get trending users (random selection for demo)
  Future<List<User>> getTrendingUsers({int count = 8}) async {
    return getRandomUsers(count: count);
  }

  /// Get recommended users based on user interactions (random for demo)
  Future<List<User>> getRecommendedUsers({int count = 10}) async {
    return getRandomUsers(count: count);
  }

  /// Get top 3 most popular users for weekly ranking
  /// Uses a pseudo-random algorithm based on week number to ensure stable weekly data
  Future<List<User>> getTop3WeeklyUsers() async {
    final allUsers = await loadAllUsers();
    if (allUsers.isEmpty) return [];

    // Get current week number to ensure stable weekly data
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final weekNumber = ((now.difference(startOfYear).inDays) / 7).floor();

    // Create a deterministic seed based on week number
    final weekSeed = weekNumber * 7 + now.year;
    final weekRandom = Random(weekSeed);

    // Create a copy of users and shuffle with week-based seed
    final shuffledUsers = List<User>.from(allUsers);

    // Custom shuffle with deterministic seed for consistent weekly results
    for (int i = shuffledUsers.length - 1; i > 0; i--) {
      final j = weekRandom.nextInt(i + 1);
      final temp = shuffledUsers[i];
      shuffledUsers[i] = shuffledUsers[j];
      shuffledUsers[j] = temp;
    }

    // Return top 3 users
    return shuffledUsers.take(3).toList();
  }

  /// Get top 10 most popular users for full ranking page
  /// Uses the same pseudo-random algorithm as getTop3WeeklyUsers to ensure consistency
  Future<List<User>> getTop10WeeklyUsers() async {
    final allUsers = await loadAllUsers();
    if (allUsers.isEmpty) return [];

    // Get current week number to ensure stable weekly data
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final weekNumber = ((now.difference(startOfYear).inDays) / 7).floor();

    // Create a deterministic seed based on week number
    final weekSeed = weekNumber * 7 + now.year;
    final weekRandom = Random(weekSeed);

    // Create a copy of users and shuffle with week-based seed
    final shuffledUsers = List<User>.from(allUsers);

    // Custom shuffle with deterministic seed for consistent weekly results
    for (int i = shuffledUsers.length - 1; i > 0; i--) {
      final j = weekRandom.nextInt(i + 1);
      final temp = shuffledUsers[i];
      shuffledUsers[i] = shuffledUsers[j];
      shuffledUsers[j] = temp;
    }

    // Generate scores for each user based on their position and week seed
    final rankedUsers = <User>[];
    for (int i = 0; i < shuffledUsers.length && i < 10; i++) {
      final user = shuffledUsers[i];
      final score = _generateWeeklyScore(i + 1, weekSeed, user.id);

      // Create a new user instance with the generated score
      final rankedUser = User(
        id: user.id,
        name: user.name,
        avatar: user.avatar,
        description: user.description,
        tags: user.tags,
        personality: user.personality,
        role: user.role,
        isOnline: user.isOnline,
        score: score,
      );

      rankedUsers.add(rankedUser);
    }

    return rankedUsers;
  }

  /// Generate a deterministic score for a user based on their rank and week
  /// Higher ranks get higher scores, with some randomness for realism
  double _generateWeeklyScore(int rank, int weekSeed, String userId) {
    // Create a deterministic random generator for this specific user and week
    final userSeed = weekSeed + userId.hashCode;
    final userRandom = Random(userSeed);

    // Base score decreases with rank
    double baseScore;
    switch (rank) {
      case 1:
        baseScore = 4.8 + (userRandom.nextDouble() * 0.2); // 4.8-5.0
        break;
      case 2:
        baseScore = 4.6 + (userRandom.nextDouble() * 0.3); // 4.6-4.9
        break;
      case 3:
        baseScore = 4.4 + (userRandom.nextDouble() * 0.4); // 4.4-4.8
        break;
      case 4:
      case 5:
        baseScore = 4.2 + (userRandom.nextDouble() * 0.5); // 4.2-4.7
        break;
      case 6:
      case 7:
        baseScore = 4.0 + (userRandom.nextDouble() * 0.5); // 4.0-4.5
        break;
      default:
        baseScore = 3.8 + (userRandom.nextDouble() * 0.6); // 3.8-4.4
        break;
    }

    // Ensure score is within valid range and round to 1 decimal place
    return double.parse((baseScore.clamp(3.0, 5.0)).toStringAsFixed(1));
  }

  /// Clear cache and reload data
  Future<void> refreshData() async {
    _allUsers = null;
    await loadAllUsers();
  }
}
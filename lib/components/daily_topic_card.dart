import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';
import '../models/topic.dart';

/// 单个话题卡片组件
class DailyTopicCard extends StatelessWidget {
  final DailyTopic topic;
  final String displayText;
  final VoidCallback onTap;

  const DailyTopicCard({
    super.key,
    required this.topic,
    required this.displayText,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: AppDesignSystem.spaceS),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDesignSystem.spaceM,
          vertical: AppDesignSystem.spaceS,
        ),
        decoration: BoxDecoration(
          color: AppDesignSystem.backgroundCard,
          borderRadius: AppDesignSystem.borderRadiusL,
          border: Border.all(
            color: AppDesignSystem.primaryYellow.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppDesignSystem.primaryYellow.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Emoji图标
            Text(
              topic.emoji,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(width: AppDesignSystem.spaceXS),
            // 话题文本
            Flexible(
              child: Text(
                displayText,
                style: const TextStyle(
                  color: AppDesignSystem.textPrimary,
                  fontSize: AppDesignSystem.fontSizeRegular,
                  fontWeight: AppDesignSystem.fontWeightMedium,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 今日话题横向滑动列表组件
class DailyTopicsList extends StatelessWidget {
  final List<DailyTopic> topics;
  final Map<String, String> localizations;
  final Locale locale;
  final Function(DailyTopic) onTopicTap;

  const DailyTopicsList({
    super.key,
    required this.topics,
    required this.localizations,
    required this.locale,
    required this.onTopicTap,
  });

  String _getTopicText(DailyTopic topic) {
    // 直接使用textKey从localizations中获取文本
    String? text = localizations[topic.textKey];

    // 如果没有找到，返回textKey作为fallback
    return text ?? topic.textKey;
  }

  @override
  Widget build(BuildContext context) {
    if (topics.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      height: 80, // 固定高度，支持最多两行文本
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceM),
        itemCount: topics.length,
        itemBuilder: (context, index) {
          final topic = topics[index];
          final displayText = _getTopicText(topic);
          
          return DailyTopicCard(
            topic: topic,
            displayText: displayText,
            onTap: () => onTopicTap(topic),
          );
        },
      ),
    );
  }
}

/// 今日话题区域组件（包含标题和卡片列表）
class DailyTopicsSection extends StatelessWidget {
  final List<DailyTopic> topics;
  final Map<String, String> localizations;
  final Locale locale;
  final Function(DailyTopic) onTopicTap;
  final String sectionTitle;

  const DailyTopicsSection({
    super.key,
    required this.topics,
    required this.localizations,
    required this.locale,
    required this.onTopicTap,
    required this.sectionTitle,
  });

  @override
  Widget build(BuildContext context) {
    if (topics.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceM),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDesignSystem.spaceS,
                  vertical: AppDesignSystem.spaceXXS,
                ),
                decoration: BoxDecoration(
                  color: AppDesignSystem.primaryYellow.withValues(alpha: 0.2),
                  borderRadius: AppDesignSystem.borderRadiusS,
                ),
                child: Text(
                  sectionTitle,
                  style: const TextStyle(
                    color: AppDesignSystem.textPrimary,
                    fontSize: AppDesignSystem.fontSizeSmall,
                    fontWeight: AppDesignSystem.fontWeightSemiBold,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppDesignSystem.spaceS),
        // 话题卡片列表
        DailyTopicsList(
          topics: topics,
          localizations: localizations,
          locale: locale,
          onTopicTap: onTopicTap,
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';
import '../models/topic.dart';

/// 单个话题卡片组件
class DailyTopicCard extends StatelessWidget {
  final DailyTopic topic;
  final String displayText;
  final VoidCallback onTap;

  const DailyTopicCard({
    super.key,
    required this.topic,
    required this.displayText,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: AppDesignSystem.spaceXS),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDesignSystem.spaceM,
        ),
        decoration: BoxDecoration(
          color: Color(0xFFEEE3CB),
          borderRadius: AppDesignSystem.borderRadiusRound,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Emoji图标
            Text(
              topic.emoji,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(width: AppDesignSystem.spaceXXS),
            // 话题文本
            Text(
              displayText,
              style: const TextStyle(
                color: AppDesignSystem.textPrimary,
                fontSize: AppDesignSystem.fontSizeSmall,
                fontWeight: AppDesignSystem.fontWeightMedium,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

/// 今日话题两行网格列表组件
class DailyTopicsList extends StatelessWidget {
  final List<DailyTopic> topics;
  final Map<String, String> localizations;
  final Locale locale;
  final Function(DailyTopic) onTopicTap;

  const DailyTopicsList({
    super.key,
    required this.topics,
    required this.localizations,
    required this.locale,
    required this.onTopicTap,
  });

  String _getTopicText(DailyTopic topic) {
    // 直接使用textKey从localizations中获取文本
    String? text = localizations[topic.textKey];

    // 如果没有找到，返回textKey作为fallback
    return text ?? topic.textKey;
  }

  @override
  Widget build(BuildContext context) {
    if (topics.isEmpty) {
      return const SizedBox.shrink();
    }

    // 计算每行显示的卡片数量
    final itemsPerRow = (topics.length / 2).ceil();
    final firstRowItems = topics.take(itemsPerRow).toList();
    final secondRowItems = topics.skip(itemsPerRow).toList();

    return SizedBox(
      height: 100, // 增加高度以容纳两行
      child: Column(
        children: [
          // 第一行
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceM),
              itemCount: firstRowItems.length,
              itemBuilder: (context, index) {
                final topic = firstRowItems[index];
                final displayText = _getTopicText(topic);

                return DailyTopicCard(
                  topic: topic,
                  displayText: displayText,
                  onTap: () => onTopicTap(topic),
                );
              },
            ),
          ),
          const SizedBox(height: AppDesignSystem.spaceS),
          // 第二行
          if (secondRowItems.isNotEmpty)
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceM),
                itemCount: secondRowItems.length,
                itemBuilder: (context, index) {
                  final topic = secondRowItems[index];
                  final displayText = _getTopicText(topic);

                  return DailyTopicCard(
                    topic: topic,
                    displayText: displayText,
                    onTap: () => onTopicTap(topic),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}

/// 今日话题区域组件（包含标题和卡片列表）
class DailyTopicsSection extends StatelessWidget {
  final List<DailyTopic> topics;
  final Map<String, String> localizations;
  final Locale locale;
  final Function(DailyTopic) onTopicTap;
  final String sectionTitle;

  const DailyTopicsSection({
    super.key,
    required this.topics,
    required this.localizations,
    required this.locale,
    required this.onTopicTap,
    required this.sectionTitle,
  });

  @override
  Widget build(BuildContext context) {
    if (topics.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceM),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppDesignSystem.spaceXXS,
                ),
                child: Text(
                  sectionTitle,
                  style: const TextStyle(
                    color: AppDesignSystem.textPrimary,
                    fontSize: 20,
                    fontWeight: AppDesignSystem.fontWeightSemiBold,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppDesignSystem.spaceS),
        // 话题卡片列表
        DailyTopicsList(
          topics: topics,
          localizations: localizations,
          locale: locale,
          onTopicTap: onTopicTap,
        ),
      ],
    );
  }
}

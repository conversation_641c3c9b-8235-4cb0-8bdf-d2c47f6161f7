import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';
import '../models/user.dart';
import '../pages/ai_detail_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// Top 3 Ranking Card Component
/// 实现颁奖台样式的排行榜卡片，展示本周最受欢迎的3位角色
class Top3RankingCard extends StatefulWidget {
  final List<User> top3Users;
  final VoidCallback? onRefresh;

  const Top3RankingCard({
    super.key,
    required this.top3Users,
    this.onRefresh,
  });

  @override
  State<Top3RankingCard> createState() => _Top3RankingCardState();
}

class _Top3RankingCardState extends State<Top3RankingCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late List<AnimationController> _itemControllers;
  late List<Animation<double>> _itemAnimations;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    // 为每个排行榜项目创建独立的动画控制器
    _itemControllers = List.generate(
        3,
        (index) => AnimationController(
              duration: Duration(milliseconds: 600 + (index * 200)),
              vsync: this,
            ));

    _itemAnimations = _itemControllers
        .map((controller) => Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(parent: controller, curve: Curves.elasticOut)))
        .toList();

    _startAnimations();
  }

  void _startAnimations() {
    _animationController.forward();

    // 依次启动每个项目的动画，添加延迟效果
    for (int i = 0; i < _itemControllers.length; i++) {
      Future.delayed(Duration(milliseconds: 200 + (i * 150)), () {
        if (mounted) {
          _itemControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(Top3RankingCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.top3Users != widget.top3Users) {
      _animationController.reset();
      for (var controller in _itemControllers) {
        controller.reset();
      }
      _startAnimations();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.top3Users.isEmpty) {
      return _buildEmptyState(context);
    }

    return Transform.scale(
      scale: 1,
      child: Container(
        margin: const EdgeInsets.fromLTRB(15, 0, 15, 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPodiumLayout(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPodiumLayout(BuildContext context) {
    // 确保至少有3个用户，不足的用空占位
    final users = List<User?>.from(widget.top3Users);
    while (users.length < 3) {
      users.add(null);
    }

    return Container(
      height: 200,
      child: Stack(
        children: [
          // 颁奖台背景
          _buildPodiumBackground(),

          // 第二名 (左侧)
          if (users[1] != null)
            Positioned(
              left: 30,
              bottom: 10,
              child: _buildRankingItem(context, users[1]!, 2, 180),
            ),

          // 第一名 (中间，最高)
          if (users[0] != null)
            Positioned(
              left: 0,
              right: 0,
              bottom: 25,
              child: Center(
                child: _buildRankingItem(context, users[0]!, 1, 200),
              ),
            ),

          // 第三名 (右侧)
          if (users[2] != null)
            Positioned(
              right: 30,
              bottom: 10,
              child: _buildRankingItem(context, users[2]!, 3, 160),
            ),
        ],
      ),
    );
  }

  Widget _buildPodiumBackground() {
    return Positioned(
      bottom: 0,
      left: 20,
      right: 20,
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              SizedBox(width: 10),
              // 第二名台阶
              Expanded(
                child: Container(
                  height: 70,
                  decoration: BoxDecoration(
                    color: AppDesignSystem.backgroundPrimary,
                    border: Border.all(
                      color: AppDesignSystem.primaryBlack,
                      width: 1.5,
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(15),
                      topRight: Radius.circular(15),
                    ),
                  ),
                ),
              ),
              // 第一名台阶
              Expanded(
                child: Container(
                  height: 100,
                  decoration: BoxDecoration(
                    color: AppDesignSystem.primaryYellow,
                    border: Border.all(
                      color: AppDesignSystem.primaryBlack,
                      width: 1.5,
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(15),
                      topRight: Radius.circular(15),
                    ),
                  ),
                ),
              ),
              // 第三名台阶
              Expanded(
                child: Container(
                  height: 70,
                  decoration: BoxDecoration(
                    color: AppDesignSystem.primaryYellow.withValues(alpha: 0.1),
                    border: Border.all(
                      color: AppDesignSystem.primaryBlack,
                      width: 1.5,
                    ),
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(15),
                      topLeft: Radius.circular(15),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 10),
            ],
          ),
          Container(
            height: 12,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppDesignSystem.primaryYellow,
              border: Border.all(
                color: AppDesignSystem.primaryBlack,
                width: 1.5,
              ),
              borderRadius: const BorderRadius.all(Radius.circular(3)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRankingItem(
      BuildContext context, User user, int rank, double height) {
    final animationIndex = rank - 1; // rank从1开始，数组从0开始

    return AnimatedBuilder(
      animation: _itemControllers[animationIndex],
      builder: (context, child) {
        final animation = _itemAnimations[animationIndex];

        return Transform.scale(
          scale: animation.value,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(15),
              onTap: () => _viewUserDetail(context, user),
              child: Stack(
                children: [
                  Container(
                    width: 100,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 排名徽章
                        // _buildRankBadge(rank),
                        const SizedBox(height: 8),

                        // 用户头像
                        Container(
                          width: 75,
                          height: 75,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AppDesignSystem.primaryBlack,
                              width: 1.5,
                            ),
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              user.avatar,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: Colors.grey[300],
                                  child: const Icon(Icons.person, size: 40),
                                );
                              },
                            ),
                          ),
                        ),
                        SizedBox(height: 8),

                        // 用户名
                        Text(
                          user.name,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: rank == 1 ? 20 : 16,
                            fontWeight: AppDesignSystem.fontWeightSemiBold,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        SizedBox(height: rank == 1 ? 55 : 45),
                      ],
                    ),
                  ),
                  Positioned(
                    bottom: -5,
                    left: 0,
                    right: 0,
                    child: Text(
                      rank.toString(),
                      strutStyle: StrutStyle(fontSize: 54),
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: rank == 1 ? 54 : 40,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRankBadge(int rank) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getRankColor(rank),
            _getRankColor(rank).withValues(alpha: 0.8),
          ],
        ),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: _getRankColor(rank).withValues(alpha: 0.4),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(-1, -1),
          ),
        ],
      ),
      child: Center(
        child: rank == 1
            ? const Icon(
                Icons.emoji_events,
                color: Colors.white,
                size: 18,
              )
            : Text(
                '#$rank',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      offset: Offset(1, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return const Color(0xFFFFD700); // 金色
      case 2:
        return const Color(0xFFC0C0C0); // 银色
      case 3:
        return const Color(0xFFCD7F32); // 铜色
      default:
        return Colors.grey;
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(15, 0, 15, 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(15),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.emoji_events_outlined,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '暂无排行榜数据',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _viewUserDetail(BuildContext context, User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIDetailPage(aiUser: user),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../theme/app_design_system.dart';

/// Emoji分类数据模型
class EmojiCategory {
  final String name;
  final String icon;
  final List<String> emojis;

  EmojiCategory({
    required this.name,
    required this.icon,
    required this.emojis,
  });
}

/// Emoji表情选择器组件
class EmojiPicker extends StatefulWidget {
  final Function(String) onEmojiSelected;
  final VoidCallback? onClose;

  const EmojiPicker({
    super.key,
    required this.onEmojiSelected,
    this.onClose,
  });

  @override
  State<EmojiPicker> createState() => _EmojiPickerState();
}

class _EmojiPickerState extends State<EmojiPicker>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Emoji分类数据
  List<EmojiCategory> _getCategories(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return [
    EmojiCategory(
      name: l10n.emojiCategorySmiling,
      icon: '😀',
      emojis: [
        '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂',
        '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩',
        '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪',
        '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨',
      ],
    ),
    EmojiCategory(
      name: l10n.emojiCategoryEmotions,
      icon: '😢',
      emojis: [
        '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
        '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢',
        '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠',
        '🥳', '😎', '🤓', '🧐', '😕', '😟', '🙁', '☹️',
      ],
    ),
    EmojiCategory(
      name: l10n.emojiCategoryGestures,
      icon: '👋',
      emojis: [
        '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️',
        '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕',
        '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜',
        '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅',
      ],
    ),
    EmojiCategory(
      name: l10n.emojiCategoryHearts,
      icon: '❤️',
      emojis: [
        '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍',
        '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖',
        '💘', '💝', '💟', '♥️', '💌', '💋', '💍', '💎',
      ],
    ),
    EmojiCategory(
      name: l10n.emojiCategoryAnimals,
      icon: '🐶',
      emojis: [
        '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼',
        '🐨', '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵',
        '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤',
        '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗',
      ],
    ),
    EmojiCategory(
      name: l10n.emojiCategoryFood,
      icon: '🍎',
      emojis: [
        '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓',
        '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅',
        '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🌽', '🥕',
        '🧄', '🧅', '🥔', '🍠', '🥐', '🍞', '🥖', '🥨',
      ],
    ),
    ];
  }

  @override
  void initState() {
    super.initState();
    // TabController的长度将在build方法中设置
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final categories = _getCategories(context);
    _tabController = TabController(
      length: categories.length,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 280,
      decoration: BoxDecoration(
        color: AppDesignSystem.backgroundCard,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDesignSystem.radiusL),
        ),
        boxShadow: [AppDesignSystem.shadowMedium],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: _buildEmojiGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(
        AppDesignSystem.spaceM,
        AppDesignSystem.spaceS,
        AppDesignSystem.spaceM,
        0
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            AppLocalizations.of(context)!.selectEmoji,
            style: AppDesignSystem.headingSmall,
          ),
          if (widget.onClose != null)
            GestureDetector(
              onTap: widget.onClose,
              child: Container(
                padding: const EdgeInsets.all(4),
                child: const Icon(
                  Icons.close,
                  size: AppDesignSystem.iconSizeM,
                  color: AppDesignSystem.textSecondary,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 48,
      child: TabBar(
        tabAlignment: TabAlignment.start,
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppDesignSystem.primaryYellow,
        indicatorWeight: 2,
        labelColor: AppDesignSystem.primaryYellow,
        unselectedLabelColor: AppDesignSystem.textSecondary,
        // labelPadding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceXS),
        tabs: _getCategories(context).map((category) {
          return Tab(
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceS),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    category.icon,
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(width: AppDesignSystem.spaceXS),
                  Text(
                    category.name,
                    style: const TextStyle(fontSize: AppDesignSystem.fontSizeSmall),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmojiGrid() {
    return TabBarView(
      controller: _tabController,
      children: _getCategories(context).map((category) {
        return Container(
          padding: const EdgeInsets.fromLTRB(
            AppDesignSystem.spaceM,
            AppDesignSystem.spaceM,
            AppDesignSystem.spaceM,
            AppDesignSystem.spaceM
          ),
          child: GridView.builder(
            padding: EdgeInsets.zero,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 8,
              crossAxisSpacing: AppDesignSystem.spaceXS,
              mainAxisSpacing: AppDesignSystem.spaceXS,
              childAspectRatio: 1,
            ),
            itemCount: category.emojis.length,
            itemBuilder: (context, index) {
              final emoji = category.emojis[index];
              return _buildEmojiItem(emoji);
            },
          ),
        );
      }).toList(),
    );
  }

  Widget _buildEmojiItem(String emoji) {
    return GestureDetector(
      onTap: () => widget.onEmojiSelected(emoji),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: AppDesignSystem.borderRadiusS,
          color: Colors.transparent,
        ),
        child: Center(
          child: Text(
            emoji,
            style: const TextStyle(fontSize: 24),
          ),
        ),
      ),
    );
  }
}

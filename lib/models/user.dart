import '../services/ai_chat_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class User {
  final String id;
  final String name;
  final String avatar;
  final String description;
  final List<String> tags;
  final String personality;
  final String role;
  final bool isOnline;
  final double score; // 评分字段，范围0.0-5.0

  User({
    required this.id,
    required this.name,
    required this.avatar,
    required this.description,
    required this.tags,
    required this.personality,
    required this.role,
    this.isOnline = false,
    this.score = 4.5, // 默认评分
  });

  /// 获取格式化的角色和个性显示文本
  /// 格式：[Role] personality
  String getRoleAndPersonality([BuildContext? context]) {
    // 获取本地化的角色名称
    String localizedRole = role.isNotEmpty 
        ? role[0].toUpperCase() + role.substring(1)
        : 'Assistant';
    
    // 获取本地化的个性描述
    String localizedPersonality = personality;
    
    // 如果有context，尝试获取本地化的个性描述
    if (context != null) {
      try {
        final l10n = AppLocalizations.of(context);
        if (l10n != null) {
          localizedPersonality = _getLocalizedPersonality(l10n);
        }
      } catch (e) {
        // 如果获取本地化失败，使用原始personality
        localizedPersonality = personality;
      }
    }
    
    if (localizedPersonality.isNotEmpty) {
      return '[$localizedRole] $localizedPersonality';
    } else {
      return '[$localizedRole] AI Assistant';
    }
  }

  /// 获取个性描述，直接返回原始数据
  String _getLocalizedPersonality(AppLocalizations l10n) {
    return personality.isNotEmpty ? personality : 'AI Assistant';
  }

  /// 将用户的role字符串映射到AI角色枚举
  AIRole getAIRole() {
    switch (role.toLowerCase()) {
      case 'lawyer':
        return AIRole.lawyer;
      case 'teacher':
        return AIRole.teacher;
      case 'broadcaster':
        return AIRole.broadcaster;
      case 'doctor':
        return AIRole.doctor;
      case 'psychologist':
        return AIRole.psychologist;
      case 'friend':
        return AIRole.friend;
      case 'assistant':
        return AIRole.assistant;
      case 'coach':
        return AIRole.coach;
      case 'chef':
        return AIRole.chef;
      case 'travelguide':
        return AIRole.travelGuide;
      case 'fitnesstrainer':
        return AIRole.fitnessTrainer;
      case 'artist':
        return AIRole.artist;
      case 'programmer':
        return AIRole.programmer;
      case 'writer':
        return AIRole.writer;
      case 'philosopher':
        return AIRole.philosopher;
      case 'comedian':
        return AIRole.comedian;
      case 'mentor':
        return AIRole.mentor;
      case 'scientist':
        return AIRole.scientist;
      case 'musician':
        return AIRole.musician;
      case 'financialadvisor':
        return AIRole.financialAdvisor;
      case 'therapist':
        return AIRole.therapist;
      case 'entrepreneur':
        return AIRole.entrepreneur;
      case 'historian':
        return AIRole.historian;
      case 'detective':
        return AIRole.detective;
      case 'librarian':
        return AIRole.librarian;
      case 'architect':
        return AIRole.architect;
      case 'gardener':
        return AIRole.gardener;
      case 'photographer':
        return AIRole.photographer;
      case 'translator':
        return AIRole.translator;
      case 'veterinarian':
        return AIRole.veterinarian;
      case 'mechanic':
        return AIRole.mechanic;
      case 'storyteller':
        return AIRole.storyteller;
      case 'critic':
        return AIRole.critic;
      case 'diplomat':
        return AIRole.diplomat;
      case 'explorer':
        return AIRole.explorer;
      case 'sage':
        return AIRole.sage;
      default:
        return AIRole.assistant;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'avatar': avatar,
      'description': description,
      'tags': tags.join(','),
      'personality': personality,
      'role': role,
      'isOnline': isOnline ? 1 : 0,
      'score': score,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      name: map['name'],
      avatar: map['avatar'],
      description: map['description'],
      tags: map['tags'].split(','),
      personality: map['personality'] ?? '',
      role: map['role'] ?? 'assistant',
      isOnline: map['isOnline'] == 1,
      score: (map['score'] as num?)?.toDouble() ?? 4.5,
    );
  }
} 
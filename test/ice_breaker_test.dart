import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:ai_chat_app/services/ice_breaker_service.dart';
import 'package:ai_chat_app/models/user.dart';

void main() {
  group('IceBreakerService Tests', () {
    late IceBreakerService service;
    late User testUser;

    setUp(() {
      service = IceBreakerService();
      testUser = User(
        id: 'test-id',
        name: 'Test AI',
        role: 'lawyer',
        avatar: 'test-avatar.png',
        description: 'Test description',
        tags: ['legal', 'professional'],
        personality: 'Professional and knowledgeable',
        isOnline: true,
      );
    });

    testWidgets('should load ice breaker data', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Builder(
          builder: (context) {
            return FutureBuilder(
              future: service.loadIceBreakerData(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.done) {
                  return const Text('Data loaded');
                }
                return const CircularProgressIndicator();
              },
            );
          },
        ),
      ));

      await tester.pumpAndSettle();
      expect(find.text('Data loaded'), findsOneWidget);
    });

    testWidgets('should get general topics', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Builder(
          builder: (context) {
            return FutureBuilder<List<String>>(
              future: service.getGeneralTopics(const Locale('en')),
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                  return Text('Found ${snapshot.data!.length} topics');
                }
                return const CircularProgressIndicator();
              },
            );
          },
        ),
      ));

      await tester.pumpAndSettle();
      expect(find.textContaining('Found'), findsOneWidget);
    });

    testWidgets('should get role-specific topics', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Builder(
          builder: (context) {
            return FutureBuilder<List<String>>(
              future: service.getRoleSpecificTopics(testUser, const Locale('en')),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.done) {
                  return Text('Role topics: ${snapshot.data?.length ?? 0}');
                }
                return const CircularProgressIndicator();
              },
            );
          },
        ),
      ));

      await tester.pumpAndSettle();
      expect(find.textContaining('Role topics:'), findsOneWidget);
    });

    testWidgets('should get mixed topics', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Builder(
          builder: (context) {
            return FutureBuilder<List<String>>(
              future: service.getMixedTopics(testUser, const Locale('en')),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return Text('Mixed topics: ${snapshot.data!.length}');
                }
                return const CircularProgressIndicator();
              },
            );
          },
        ),
      ));

      await tester.pumpAndSettle();
      expect(find.textContaining('Mixed topics:'), findsOneWidget);
    });

    testWidgets('should get recommended topics (max 5)', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Builder(
          builder: (context) {
            return FutureBuilder<List<String>>(
              future: service.getRecommendedTopics(testUser, const Locale('en')),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final count = snapshot.data!.length;
                  return Text('Recommended: $count (max 5: ${count <= 5})');
                }
                return const CircularProgressIndicator();
              },
            );
          },
        ),
      ));

      await tester.pumpAndSettle();
      expect(find.textContaining('max 5: true'), findsOneWidget);
    });
  });
}

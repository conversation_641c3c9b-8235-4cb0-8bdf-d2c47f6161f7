import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:ai_chat_app/pages/chat_page.dart';
import 'package:ai_chat_app/providers/chat_provider.dart';
import 'package:ai_chat_app/providers/language_provider.dart';
import 'package:ai_chat_app/models/user.dart';

void main() {
  group('Chat Emoji Integration Tests', () {
    late User testAiUser;

    setUp(() {
      testAiUser = User(
        id: 'test_ai',
        name: 'Test AI',
        avatar: 'test_avatar.png',
        description: 'Test AI Description',
        tags: ['test'],
        personality: 'Friendly and helpful',
        role: 'assistant',
        isOnline: true,
      );
    });

    testWidgets('Should show emoji button and toggle emoji picker', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ChatProvider()),
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ],
          child: MaterialApp(
            home: ChatPage(aiUser: testAiUser),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 查找emoji按钮
      expect(find.byIcon(Icons.emoji_emotions_outlined), findsOneWidget);

      // 点击emoji按钮
      await tester.tap(find.byIcon(Icons.emoji_emotions_outlined));
      await tester.pumpAndSettle();

      // 验证emoji选择器是否显示
      expect(find.text('选择表情'), findsOneWidget);
      expect(find.text('笑脸'), findsOneWidget);

      // 验证按钮图标是否变为键盘图标
      expect(find.byIcon(Icons.keyboard), findsOneWidget);

      // 再次点击按钮关闭emoji选择器
      await tester.tap(find.byIcon(Icons.keyboard));
      await tester.pumpAndSettle();

      // 验证emoji选择器是否隐藏
      expect(find.text('选择表情'), findsNothing);
      expect(find.byIcon(Icons.emoji_emotions_outlined), findsOneWidget);
    });

    testWidgets('Should insert emoji into text field', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ChatProvider()),
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ],
          child: MaterialApp(
            home: ChatPage(aiUser: testAiUser),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 在输入框中输入一些文本
      await tester.enterText(find.byType(TextField), 'Hello ');
      await tester.pump();

      // 点击emoji按钮
      await tester.tap(find.byIcon(Icons.emoji_emotions_outlined));
      await tester.pumpAndSettle();

      // 选择一个emoji
      await tester.tap(find.byWidgetPredicate((widget) => 
        widget is Text && 
        widget.data == '😀' && 
        widget.style?.fontSize == 24));
      await tester.pumpAndSettle();

      // 验证文本是否包含emoji
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, equals('Hello 😀'));

      // 验证emoji选择器是否关闭
      expect(find.text('选择表情'), findsNothing);
    });

    testWidgets('Should close emoji picker when tapping outside', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ChatProvider()),
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ],
          child: MaterialApp(
            home: ChatPage(aiUser: testAiUser),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 点击emoji按钮打开选择器
      await tester.tap(find.byIcon(Icons.emoji_emotions_outlined));
      await tester.pumpAndSettle();

      // 验证emoji选择器是否显示
      expect(find.text('选择表情'), findsOneWidget);

      // 点击页面其他区域
      await tester.tapAt(const Offset(100, 100));
      await tester.pumpAndSettle();

      // 验证emoji选择器是否关闭
      expect(find.text('选择表情'), findsNothing);
    });
  });
}
